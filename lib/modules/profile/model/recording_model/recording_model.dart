import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';

part 'recording_model.freezed.dart';
part 'recording_model.g.dart';

LyricsData? recordingLyricsDataFromJson(Object? json) {
  if (json == null || json is! Map<String, dynamic>) {
    return null;
  }
  return LyricsData.fromJson(json);
}

Map<String, dynamic>? recordingLyricsDataToJson(LyricsData? data) {
  return data?.toJson();
}

@freezed
class RecordingModel with _$RecordingModel {
  factory RecordingModel({
    @JsonKey(name: '_id') required String id,
    @Json<PERSON>ey(name: 'title') @Default('') String title,
    @J<PERSON><PERSON><PERSON>(name: 'singer') @Default('') String singer,
    @JsonKey(name: 'final_video_file_path') @Default('') String finalVideoFilePath,
    @<PERSON>son<PERSON><PERSON>(name: 'final_mixed_audio_path') @Default('') String finalMixedAudioPath,
    @Json<PERSON>ey(name: 'vocal_filter_name') @Default('') String vocalFilterName,
    @Json<PERSON>ey(name: 'genre') @Default('') String genre,
    @Json<PERSON>ey(name: 'master_song_id') @Default('') String masterSongId,
    @JsonKey(name: 'thumbnail_path') @Default('') String thumbnailPath,
    @JsonKey(name: 'tempo') @Default('') String tempo,
    @JsonKey(name: 'scale') @Default('') String scale,
    @JsonKey(name: 'is_final_save') @Default(false) bool isFinalSave,
    @JsonKey(name: 'is_deleted') @Default(false) bool isDeleted,
    @JsonKey(name: 'media_type') @Default('audio') String mediaType,
    @JsonKey(name: 'created_at') @Default(0) int createdAt,
    @JsonKey(name: 'feed_type') @Default('') String feedType,
    @JsonKey(name: 'lyrics_timeline_file_path') @Default('') String lyricsJsonPath,
    @JsonKey(
      name: 'lyrics_data',
      fromJson: recordingLyricsDataFromJson,
      toJson: recordingLyricsDataToJson,
    )
    LyricsData? lyricsData,
  }) = _RecordingModel;

  factory RecordingModel.fromJson(Map<String, dynamic> json) => _$$RecordingModelImplFromJson(json);

  Map<String, dynamic> toJson() => _$$RecordingModelImplToJson(this as _$RecordingModelImpl);
}
