import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/modules/profile/bloc/cubit/profile_practice_selection_cubit.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/ui/widget/profile_recording_item.dart';
import 'package:melodyze/modules/profile/ui/widget/practice_recording_player_screen.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

class ProfileFinalRecordingList extends StatefulWidget {
  final List<RecordingModel> recordings;
  const ProfileFinalRecordingList({
    super.key,
    required this.recordings,
  });

  @override
  State<ProfileFinalRecordingList> createState() => _ProfileFinalRecordingListState();
}

class _ProfileFinalRecordingListState extends State<ProfileFinalRecordingList> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 0.0),
      child: Column(
        children: [
          Expanded(
            child: ListView.separated(
              itemCount: widget.recordings.length,
              padding: EdgeInsets.only(bottom: 16.0, top: 16.0),
              itemBuilder: (context, index) {
                final recording = widget.recordings[index];
                // Log if lyrics data is present
                if (recording.lyricsData != null) {
                  logger.d('[FinalRecordingList] Lyrics data found for recording: ${recording.id}');
                } else {
                  logger.d('[FinalRecordingList] No lyrics data for recording: ${recording.id}');
                }
                return Padding(
                  padding: EdgeInsets.only(
                    left: 16.0,
                    bottom: index == widget.recordings.length - 1 ? 120.0 : 0.0,
                  ),
                  child: InkWell(
                    onTap: () {
                      logger.d('[FinalRecordingList] INKWELL TAP detected for recording: ${recording.id}');
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) {
                            logger.d('[FinalRecordingList] Building PracticeRecordingPlayerScreen for recording: ${recording.id}');
                            return PracticeRecordingPlayerScreen(recording: recording);
                          },
                        ),
                      );
                      logger.d('[FinalRecordingList] Navigation push completed for recording: ${recording.id}');
                    },
                    child: RecordingListItem(
                      type: RecordingListType.finalRecording,
                      recording: recording,
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 12.0),
                child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void deactivate() {
    context.read<ProfilePracticeSelectionCubit>().onPracticeRecordingUnselected();
    super.deactivate();
  }
}
